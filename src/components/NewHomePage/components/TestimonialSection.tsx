"use client";

import Pill from "@/components/globals/DSComponentsV0/Pill";
import SectionContainer from "@/components/globals/SectionContainer";
import {
  BodyLarge,
  BodyMedium,
  BodySmall,
  HeadingLarge,
  HeadingXLarge,
} from "@/components/UI/Typography";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import TestimonialCard from "./TestimonialCard";

export type TestimonialSectionProps = {
  pill_Content: string;
  title: string;
  story: {
    title: string;
    points: string[];
  };
  testimonials: {
    name: string;
    content?: string;
    video_url?: string;
  }[];
};

const TestimonialSection = ({
  pill_Content,
  title,
  story,
  testimonials,
}: TestimonialSectionProps) => {
  const storyPoints = story.points;

  const [currentIndex, setCurrentIndex] = useState(0);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll story points
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % storyPoints.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [storyPoints.length]);

  // Scroll-based testimonial animation - slow horizontal scroll
  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current) return;

      const container = containerRef.current;
      const rect = container.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Calculate scroll progress when container is in view
      const scrollStart = -rect.top;
      const scrollEnd = rect.height - windowHeight;

      if (scrollStart > 0 && scrollStart < scrollEnd) {
        // Slow down the scroll progress for slower testimonial movement
        const progress = (scrollStart / scrollEnd) * 0.5; // Make it 50% slower
        setScrollProgress(Math.max(0, Math.min(progress, 1)));
      } else if (scrollStart <= 0) {
        setScrollProgress(0);
      } else if (scrollStart >= scrollEnd) {
        setScrollProgress(0.5); // Max progress at 50% for slower movement
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Initial call
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Detect mobile view
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768); // md breakpoint
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Get the style for each position in the list animation
  const getPositionStyle = (position: number) => {
    switch (position) {
      case 0:
        return "text-primary-600 bg-transparent opacity-30";
      case 1:
        return "text-primary-700 bg-transparent opacity-60";
      case 2:
        return "text-white bg-secondary-400 py-3 md:py-5 px-4 md:px-9 shadow-md opacity-100";
      case 3:
        return "text-primary-700 bg-transparent opacity-60";
      case 4:
        return "text-primary-600 bg-transparent opacity-30";
      default:
        return "text-primary-600 bg-transparent opacity-0";
    }
  };

  // Simple calculation for testimonial translation based on scroll - slow movement
  const cardWidth = isMobile ? 310 : 450; // Approximate card width including gap
  const maxScroll = (testimonials.length - 1) * cardWidth; // Total scrollable distance
  const translateX = -(scrollProgress * maxScroll * 2); // Multiply by 2 for more movement range

  // Calculate scroll height - make it taller for slower testimonial scrolling
  const scrollHeight =
    typeof window !== "undefined" ? window.innerHeight * 2 : 2000;

  return (
    <div
      ref={containerRef}
      style={{ height: `${scrollHeight}px` }}
      className="mb-9 md:mb-24"
    >
      <div className="sticky top-0 md:top-28 bg-secondary-100 pt-10 w-full overflow-hidden pb-16">
        <SectionContainer className="flex flex-col mb-6 md:mb-16 items-center gap-2">
          <Pill
            pill={pill_Content}
            border={false}
            textColor="text-secondary-400"
            bgColor="bg-white"
            className="mb-3"
          />
          <HeadingXLarge className="text-primary-800 text-center font-medium">
            {title}
          </HeadingXLarge>
        </SectionContainer>

        {/* ------------------ Main Content ------------------ */}
        <div className="flex flex-col items-center md:flex-row gap-6 md:gap-11 w-full px-6 md:px-0">
          {/* ----------- Story Section ----------- */}
          <div className="w-full md:w-3/5 flex flex-col items-end">
            <div className="flex flex-col-reverse md:flex-col  md:gap-4 md:pl-28 w-full justify-center items-center md:items-start">
              <HeadingLarge className="text-primary-800 font-semibold inline-flex items-center gap-1.5">
                {story.title}
                <span>
                  <Image
                    src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5b1b-025e-7670-869e-d2e797eb7ec7/Untitled_Artwork 40 2.png"
                    alt="brush stroke"
                    width={40}
                    height={40}
                  />
                </span>
              </HeadingLarge>

              {/* Rotating Story Points */}
              <div className="relative max-h-[400px] flex items-center overflow-hidden w-full">
                <div className="w-full relative min-h-[200px] md:min-h-[400px] flex flex-col justify-center items-center md:items-start md:mr-16">
                  {storyPoints.map((point, index) => {
                    const relativePosition =
                      (index - currentIndex + storyPoints.length) %
                      storyPoints.length;
                    const position =
                      relativePosition < 5 ? relativePosition : -1;
                    const isVisible = position >= 0;

                    // Calculate transform based on position with larger spacing to prevent overlap
                    const getTransform = (pos: number) => {
                      // Increased spacing to accommodate multi-line content
                      const baseSpacing = isMobile ? 50 : 80;
                      const centerOffset = pos - 2; // Position 2 is center (0 offset)
                      const translateY = centerOffset * baseSpacing;

                      // Add scale for depth effect
                      const scale = pos === 2 ? 1 : 0.95;

                      return `translateY(${translateY}px) scale(${scale})`;
                    };

                    return (
                      <div
                        key={index}
                        className={`absolute left-0 right-0 transition-all duration-700 ease-in-out ${
                          isVisible ? "opacity-100" : "opacity-0 pointer-events-none"
                        }`}
                        style={{
                          transform: isVisible ? getTransform(position) : 'translateY(0px) scale(0.9)',
                          zIndex: position === 2 ? 10 : 5 - Math.abs(position - 2)
                        }}
                      >
                        <div
                          className={`rounded-full text-center md:text-start transition-all duration-700 ease-in-out leading-relaxed ${getPositionStyle(
                            position
                          )}`}
                        >
                          {isMobile ? (
                            <BodySmall
                              className={`${position === 2 ? "font-medium" : ""} leading-relaxed`}
                            >
                              {point}
                            </BodySmall>
                          ) : position === 2 ? (
                            <BodyLarge weight="medium" className="leading-relaxed">
                              {point}
                            </BodyLarge>
                          ) : (
                            <BodyMedium className="leading-relaxed">{point}</BodyMedium>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* ----------- Testimonials Section ----------- */}
          <div className="w-full  md:w-2/5">
            <div className="flex overflow-hidden md:p-6 md:bg-white md:rounded-l-xl justify-center md:justify-start">
              {/* Desktop horizontal scroll animation */}
              <div
                className="hidden md:flex gap-8 transition-transform h-full duration-500 ease-out"
                style={{ transform: `translateX(${translateX}px)` }}
              >
                {testimonials.map((testimonial, index) => (
                  <TestimonialCard
                    key={index}
                    name={testimonial.name}
                    statement={testimonial.content}
                    video_url={testimonial.video_url}
                  />
                ))}
              </div>

              {/* Mobile horizontal scroll animation */}
              <div
                className="flex md:hidden gap-5 transition-transform duration-500 ease-out pb-4 w-full"
                style={{ transform: `translateX(${translateX}px)` }}
              >
                {testimonials.map((testimonial, index) => (
                  <TestimonialCard
                    key={index}
                    name={testimonial.name}
                    statement={testimonial.content}
                    video_url={testimonial.video_url}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialSection;

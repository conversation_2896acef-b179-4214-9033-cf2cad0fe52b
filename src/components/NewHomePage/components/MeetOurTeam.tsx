import {
  BodyMedium,
  DisplayMedium,
  HeadingSmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import React from "react";
import Image from "next/image";
import { htmlParser } from "@/utils/htmlParser";
import SectionContainer from "@/components/globals/SectionContainer";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";

type MeetOurTeamProps = {
  title?: string;
  image: string;
  description?: string;
};

const MeetOurTeam: React.FC<MeetOurTeamProps> = ({
  title = "Meet The Team",
  image,
  description = "",
}) => {
  return (
    <SectionContainerLarge className="!mb-9 md:!mb-24 flex flex-col  overflow-hidden ">
      <SectionContainerLarge className="!mb-8 md:!mb-14">
        <HeadingXLarge className="text-primary-800 text-center font-semibold mb-2 md:mb-4">
          {title}
        </HeadingXLarge>
        <HeadingSmall>
          {description &&
            htmlParser(description, {
              components: {
                p: BodyMedium,
              },
              classNames: {
                p: "text-primary-800 text-center font-regular",
              },
            })}
        </HeadingSmall>
      </SectionContainerLarge>
      {/* Title */}

      {/* Team Image */}
      <SectionContainerMedium className="!px-0 md:px-0 !mb-0 md:!mb-0">
        <div className="relative z-10 w-full h-[182px] md:h-[540px] rounded-xl shadow-lg overflow-hidden">
          <Image src={image} alt="our-team" fill className="object-cover" />
        </div>
      </SectionContainerMedium>
    </SectionContainerLarge>
  );
};

export default MeetOurTeam;
